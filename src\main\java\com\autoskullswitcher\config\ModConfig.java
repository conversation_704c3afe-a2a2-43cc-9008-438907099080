package com.autoskullswitcher.config;

import com.autoskullswitcher.AutoSkullSwitcher;
import net.neoforged.neoforge.common.ModConfigSpec;

public class ModConfig {
    
    public static final ModConfigSpec.Builder BUILDER = new ModConfigSpec.Builder();
    public static final ModConfigSpec SPEC;
    
    // 配置项定义
    public static final ModConfigSpec.IntValue SWITCHING_INTERVAL;
    public static final ModConfigSpec.BooleanValue DEFAULT_RANDOM_ORDER;
    public static final ModConfigSpec.BooleanValue SHOW_CHAT_MESSAGES;
    
    static {
        BUILDER.push("General Settings");
        
        SWITCHING_INTERVAL = BUILDER
                .comment("Time interval between skull switches (in seconds)")
                .defineInRange("switching_interval", 1, 1, 60);
        
        DEFAULT_RANDOM_ORDER = BUILDER
                .comment("Default switching order mode (true = random, false = sequential)")
                .define("default_random_order", false);
        
        SHOW_CHAT_MESSAGES = BUILDER
                .comment("Show status messages in chat")
                .define("show_chat_messages", true);
        
        BUILDER.pop();
        SPEC = BUILDER.build();
    }
    
    /**
     * 获取头颅切换间隔（秒）
     */
    public static long getSwitchingInterval() {
        return SWITCHING_INTERVAL.get();
    }
    
    /**
     * 获取默认随机模式设置
     */
    public static boolean getDefaultRandomOrder() {
        return DEFAULT_RANDOM_ORDER.get();
    }
    
    /**
     * 是否显示聊天消息
     */
    public static boolean shouldShowChatMessages() {
        return SHOW_CHAT_MESSAGES.get();
    }
    
    /**
     * 设置头颅切换间隔
     */
    public static void setSwitchingInterval(int seconds) {
        if (seconds >= 1 && seconds <= 60) {
            SWITCHING_INTERVAL.set(seconds);
            AutoSkullSwitcher.LOGGER.info("Switching interval set to {} seconds", seconds);
        } else {
            AutoSkullSwitcher.LOGGER.warn("Invalid switching interval: {}. Must be between 1 and 60 seconds", seconds);
        }
    }
    
    /**
     * 设置默认随机模式
     */
    public static void setDefaultRandomOrder(boolean random) {
        DEFAULT_RANDOM_ORDER.set(random);
        AutoSkullSwitcher.LOGGER.info("Default random order set to: {}", random);
    }
    
    /**
     * 设置是否显示聊天消息
     */
    public static void setShowChatMessages(boolean show) {
        SHOW_CHAT_MESSAGES.set(show);
        AutoSkullSwitcher.LOGGER.info("Show chat messages set to: {}", show);
    }
}