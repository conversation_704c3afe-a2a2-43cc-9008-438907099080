# Sets default memory used for gradle commands. Can be overridden by user or command line properties.
# This is required to provide enough memory for the Minecraft decompilation process.
org.gradle.jvmargs=-Xmx3G
org.gradle.daemon=false
org.gradle.parallel=false
org.gradle.caching=false
org.gradle.configuration-cache=false

# ModLoader Properties
# The version of Minecraft you are developing for
minecraft_version=1.21.4
# The Minecraft version range can use any version of Minecraft from 1.21.4 onwards
minecraft_version_range=[1.21.4,1.22)
# The NeoForge version you are developing for
neo_version=21.4.147
# The NeoForge version range can use any version of NeoForge from 21.4.147 onwards
neo_version_range=[21.4.147,21.5)
# The loader version range can only use the major version of NeoForge, to allow for more compatibility
loader_version_range=[1,)

# Mod Properties
# The unique mod identifier for the mod. Must be lowercase in English locale. Must fit the regex [a-z][a-z0-9_]{1,63}
# Must match the String constant located in the main mod class annotated with @Mod.
mod_id=autoskullswitcher
# The human-readable display name for the mod.
mod_name=Auto Skull Switcher
# The license of the mod. Review your options at https://choosealicense.com/. All Rights Reserved is the default.
mod_license=MIT
# The mod version. See https://semver.org/
mod_version=1.0.0
# The group ID for the mod. It is only important when publishing as an artifact to a Maven repository.
# This should match the base package used for the mod sources.
# See https://maven.apache.org/guides/mini/guide-naming-conventions.html
mod_group_id=com.autoskullswitcher
# The authors of the mod
mod_authors=Auto Skull Switcher Team
# The description text for the mod (multi line allowed)
mod_description=A mod that automatically switches player head items from inventory, supporting sequential and random order switching.
