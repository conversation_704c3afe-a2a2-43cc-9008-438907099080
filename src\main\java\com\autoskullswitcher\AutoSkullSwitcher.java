package com.autoskullswitcher;

import com.autoskullswitcher.config.ModConfig;
import com.autoskullswitcher.logic.SkullSwitcher;
import net.neoforged.bus.api.IEventBus;
import net.neoforged.fml.ModContainer;
import net.neoforged.fml.common.Mod;
import net.neoforged.fml.config.ModConfig.Type;
import net.neoforged.fml.event.lifecycle.FMLCommonSetupEvent;
import net.neoforged.neoforge.common.NeoForge;
import net.neoforged.neoforge.event.server.ServerStoppingEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Mod("autoskullswitcher")
public class AutoSkullSwitcher {
    public static final String MOD_ID = "autoskullswitcher";
    public static final Logger LOGGER = LogManager.getLogger();

    public AutoSkullSwitcher(IEventBus modEventBus, ModContainer modContainer) {
        // Register the commonSetup method for modloading
        modEventBus.addListener(this::commonSetup);
        
        // Register config
        modContainer.registerConfig(Type.CLIENT, ModConfig.SPEC);
        
        // Register shutdown event
        NeoForge.EVENT_BUS.addListener(this::onServerStopping);

        LOGGER.info("Auto Skull Switcher mod initialized!");
    }

    private void commonSetup(final FMLCommonSetupEvent event) {
        // Some common setup code
        LOGGER.info("Auto Skull Switcher common setup complete!");
    }
    
    private void onServerStopping(final ServerStoppingEvent event) {
        // Clean up resources when server stops
        SkullSwitcher.cleanup();
        LOGGER.info("Auto Skull Switcher cleanup complete!");
    }
}