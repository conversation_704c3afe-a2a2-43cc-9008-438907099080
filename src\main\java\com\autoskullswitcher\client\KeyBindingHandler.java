package com.autoskullswitcher.client;

import com.autoskullswitcher.AutoSkullSwitcher;
import com.autoskullswitcher.logic.SkullSwitcher;
import net.minecraft.client.KeyMapping;
import net.minecraft.client.Minecraft;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.neoforge.client.event.InputEvent;
import net.neoforged.neoforge.client.event.RegisterKeyMappingsEvent;
import org.lwjgl.glfw.GLFW;

public class KeyBindingHandler {
    
    // 定义按键绑定
    public static final KeyMapping TOGGLE_SKULL_SWITCHER = new KeyMapping(
        "key.autoskullswitcher.toggle_switcher", // 翻译键
        GLFW.GLFW_KEY_L, // L键
        "key.categories.autoskullswitcher" // 按键分类
    );
    
    public static final KeyMapping CHANGE_ORDER = new KeyMapping(
        "key.autoskullswitcher.change_order", // 翻译键  
        GLFW.GLFW_KEY_K, // K键
        "key.categories.autoskullswitcher" // 按键分类
    );
    
    public static void init() {
        AutoSkullSwitcher.LOGGER.info("Key bindings initialized!");
    }
    
    public void registerKeyMappings(RegisterKeyMappingsEvent event) {
        event.register(TOGGLE_SKULL_SWITCHER);
        event.register(CHANGE_ORDER);
        AutoSkullSwitcher.LOGGER.info("Key mappings registered!");
    }

    @SubscribeEvent
    public void onKeyInput(InputEvent.Key event) {
        Minecraft minecraft = Minecraft.getInstance();
        
        // 确保玩家在游戏中且不在GUI界面
        if (minecraft.level == null || minecraft.player == null || minecraft.screen != null) {
            return;
        }
        
        // 检查L键是否被按下 - 切换自动头颅更换
        if (TOGGLE_SKULL_SWITCHER.consumeClick()) {
            AutoSkullSwitcher.LOGGER.info("Toggling skull switcher...");
            SkullSwitcher.toggleSkullSwitching(minecraft.player);
        }
        
        // 检查K键是否被按下 - 切换顺序模式
        if (CHANGE_ORDER.consumeClick()) {
            AutoSkullSwitcher.LOGGER.info("Changing skull switching order...");
            SkullSwitcher.toggleSwitchingOrder(minecraft.player);
        }
    }
}