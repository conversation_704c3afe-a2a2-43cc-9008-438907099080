package com.autoskullswitcher.client;

import com.autoskullswitcher.AutoSkullSwitcher;
import net.neoforged.api.distmarker.Dist;
import net.neoforged.bus.api.IEventBus;
import net.neoforged.fml.ModContainer;
import net.neoforged.fml.common.Mod;
import net.neoforged.fml.event.lifecycle.FMLClientSetupEvent;
import net.neoforged.neoforge.common.NeoForge;

@Mod(value = AutoSkullSwitcher.MOD_ID, dist = Dist.CLIENT)
public class AutoSkullSwitcherClient {
    
    public AutoSkullSwitcherClient(IEventBus modEventBus, ModContainer modContainer) {
        modEventBus.addListener(this::clientSetup);

        // Register key mapping registration event on mod event bus
        KeyBindingHandler keyHandler = new KeyBindingHandler();
        modEventBus.addListener(keyHandler::registerKeyMappings);

        // Register client-side event handlers on NeoForge event bus
        NeoForge.EVENT_BUS.register(keyHandler);

        AutoSkullSwitcher.LOGGER.info("Auto Skull Switcher client initialized!");
    }
    
    private void clientSetup(final FMLClientSetupEvent event) {
        // Initialize key bindings
        KeyBindingHandler.init();
        
        AutoSkullSwitcher.LOGGER.info("Auto Skull Switcher client setup complete!");
    }
}