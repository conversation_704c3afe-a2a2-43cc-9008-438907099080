package com.autoskullswitcher.logic;

import com.autoskullswitcher.AutoSkullSwitcher;
import com.autoskullswitcher.config.ModConfig;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.PlayerHeadItem;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class SkullSwitcher {
    
    private static boolean isActive = false;
    private static boolean isRandomOrder = false;
    private static int currentSkullIndex = 0;
    private static List<ItemStack> availableSkulls = new ArrayList<>();
    private static ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static ScheduledFuture<?> switchingTask = null;
    
    /**
     * 切换头颅自动更换功能的开启/关闭状态
     */
    public static void toggleSkullSwitching(Player player) {
        if (isActive) {
            stopSkullSwitching(player);
        } else {
            startSkullSwitching(player);
        }
    }
    
    /**
     * 开始自动头颅更换
     */
    public static void startSkullSwitching(Player player) {
        // 扫描背包中的头颅物品
        scanForSkulls(player);
        
        if (availableSkulls.isEmpty()) {
            player.displayClientMessage(Component.translatable("autoskullswitcher.message.no_skulls"), false);
            AutoSkullSwitcher.LOGGER.info("No skulls found in inventory");
            return;
        }
        
        isActive = true;
        currentSkullIndex = 0;
        
        // 如果是随机模式，打乱列表
        if (isRandomOrder) {
            Collections.shuffle(availableSkulls);
        }
        
        // 立即装备第一个头颅
        equipNextSkull(player);
        
        // 启动定时任务
        startScheduledSwitching(player);
        
        String orderMode = isRandomOrder ? "random" : "sequential";
        player.displayClientMessage(Component.translatable("autoskullswitcher.message.started", orderMode, availableSkulls.size()), false);
        AutoSkullSwitcher.LOGGER.info("Started skull switching in {} mode with {} skulls", orderMode, availableSkulls.size());
    }
    
    /**
     * 停止自动头颅更换
     */
    public static void stopSkullSwitching(Player player) {
        isActive = false;
        
        // 取消定时任务
        if (switchingTask != null && !switchingTask.isCancelled()) {
            switchingTask.cancel(false);
            switchingTask = null;
        }
        
        player.displayClientMessage(Component.translatable("autoskullswitcher.message.stopped"), false);
        AutoSkullSwitcher.LOGGER.info("Stopped skull switching");
    }
    
    /**
     * 切换排序模式（顺序/随机）
     */
    public static void toggleSwitchingOrder(Player player) {
        isRandomOrder = !isRandomOrder;
        String newMode = isRandomOrder ? "random" : "sequential";
        
        player.displayClientMessage(Component.translatable("autoskullswitcher.message.order_changed", newMode), false);
        AutoSkullSwitcher.LOGGER.info("Switched to {} order mode", newMode);
        
        // 如果当前正在运行，重新开始以应用新的排序
        if (isActive) {
            stopSkullSwitching(player);
            startSkullSwitching(player);
        }
    }
    
    /**
     * 扫描玩家背包中的头颅物品
     */
    private static void scanForSkulls(Player player) {
        availableSkulls.clear();
        Inventory inventory = player.getInventory();
        
        // 遍历背包中的所有物品
        for (int i = 0; i < inventory.getContainerSize(); i++) {
            ItemStack stack = inventory.getItem(i);
            
            if (isSkullItem(stack)) {
                // 创建副本以避免修改原始物品堆
                availableSkulls.add(stack.copy());
                AutoSkullSwitcher.LOGGER.debug("Found skull item: {} in slot {}", stack.getDisplayName().getString(), i);
            }
        }
        
        AutoSkullSwitcher.LOGGER.info("Found {} skull items in inventory", availableSkulls.size());
    }
    
    /**
     * 检查物品是否为头颅类型
     */
    private static boolean isSkullItem(ItemStack stack) {
        if (stack.isEmpty()) {
            return false;
        }
        
        // 检查常见的头颅物品类型
        return stack.getItem() == Items.PLAYER_HEAD ||
               stack.getItem() == Items.ZOMBIE_HEAD ||
               stack.getItem() == Items.SKELETON_SKULL ||
               stack.getItem() == Items.WITHER_SKELETON_SKULL ||
               stack.getItem() == Items.CREEPER_HEAD ||
               stack.getItem() == Items.DRAGON_HEAD ||
               stack.getItem() instanceof PlayerHeadItem;
    }
    
    /**
     * 装备下一个头颅
     */
    private static void equipNextSkull(Player player) {
        if (availableSkulls.isEmpty()) {
            stopSkullSwitching(player);
            return;
        }
        
        ItemStack skullToEquip = availableSkulls.get(currentSkullIndex);
        
        // 装备头颅到头盔位置
        ItemStack currentHelmet = player.getItemBySlot(EquipmentSlot.HEAD);
        
        // 如果当前有头盔且不是头颅，先尝试放回背包
        if (!currentHelmet.isEmpty() && !isSkullItem(currentHelmet)) {
            if (!player.getInventory().add(currentHelmet)) {
                // 背包满了，丢弃当前头盔
                player.drop(currentHelmet, false);
            }
        }
        
        // 装备新头颅
        player.setItemSlot(EquipmentSlot.HEAD, skullToEquip.copy());
        
        AutoSkullSwitcher.LOGGER.debug("Equipped skull: {}", skullToEquip.getDisplayName().getString());
        
        // 更新索引
        currentSkullIndex = (currentSkullIndex + 1) % availableSkulls.size();
        
        // 如果是随机模式且已经循环完一轮，重新打乱
        if (isRandomOrder && currentSkullIndex == 0 && availableSkulls.size() > 1) {
            Collections.shuffle(availableSkulls);
        }
    }
    
    /**
     * 启动定时切换任务
     */
    private static void startScheduledSwitching(Player player) {
        if (switchingTask != null && !switchingTask.isCancelled()) {
            switchingTask.cancel(false);
        }
        
        switchingTask = scheduler.scheduleWithFixedDelay(
            () -> {
                // 确保在主线程中执行
                Minecraft.getInstance().execute(() -> {
                    if (isActive && player != null && !player.isRemoved()) {
                        equipNextSkull(player);
                    }
                });
            },
            ModConfig.getSwitchingInterval(),
            ModConfig.getSwitchingInterval(),
            TimeUnit.SECONDS
        );
    }
    
    /**
     * 获取当前状态信息
     */
    public static boolean isActive() {
        return isActive;
    }
    
    public static boolean isRandomOrder() {
        return isRandomOrder;
    }
    
    public static int getAvailableSkullsCount() {
        return availableSkulls.size();
    }
    
    /**
     * 清理资源
     */
    public static void cleanup() {
        if (switchingTask != null && !switchingTask.isCancelled()) {
            switchingTask.cancel(true);
        }
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}