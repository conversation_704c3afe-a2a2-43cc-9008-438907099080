package com.autoskullswitcher.logic;

import com.autoskullswitcher.AutoSkullSwitcher;
import com.autoskullswitcher.config.ModConfig;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.PlayerHeadItem;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class SkullSwitcher {

    private static boolean isActive = false;
    private static boolean isRandomOrder = false;
    private static int currentSkullIndex = 0;
    private static List<Integer> availableSkullSlots = new ArrayList<>(); // 存储背包槽位而不是物品副本
    private static ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static ScheduledFuture<?> switchingTask = null;
    
    /**
     * 切换头颅自动更换功能的开启/关闭状态
     */
    public static void toggleSkullSwitching(Player player) {
        if (isActive) {
            stopSkullSwitching(player);
        } else {
            startSkullSwitching(player);
        }
    }
    
    /**
     * 开始自动头颅更换
     */
    public static void startSkullSwitching(Player player) {
        // 扫描背包中的头颅物品
        scanForSkulls(player);

        if (availableSkullSlots.isEmpty()) {
            player.displayClientMessage(Component.translatable("autoskullswitcher.message.no_skulls"), false);
            AutoSkullSwitcher.LOGGER.info("No skulls found in inventory");
            return;
        }

        isActive = true;
        currentSkullIndex = 0;

        // 如果是随机模式，打乱列表
        if (isRandomOrder) {
            Collections.shuffle(availableSkullSlots);
        }

        // 立即装备第一个头颅
        equipNextSkull(player);

        // 启动定时任务
        startScheduledSwitching(player);

        String orderMode = isRandomOrder ? "random" : "sequential";
        player.displayClientMessage(Component.translatable("autoskullswitcher.message.started", orderMode, availableSkullSlots.size()), false);
        AutoSkullSwitcher.LOGGER.info("Started skull switching in {} mode with {} skulls", orderMode, availableSkullSlots.size());
    }
    
    /**
     * 停止自动头颅更换
     */
    public static void stopSkullSwitching(Player player) {
        isActive = false;
        
        // 取消定时任务
        if (switchingTask != null && !switchingTask.isCancelled()) {
            switchingTask.cancel(false);
            switchingTask = null;
        }
        
        player.displayClientMessage(Component.translatable("autoskullswitcher.message.stopped"), false);
        AutoSkullSwitcher.LOGGER.info("Stopped skull switching");
    }
    
    /**
     * 切换排序模式（顺序/随机）
     */
    public static void toggleSwitchingOrder(Player player) {
        isRandomOrder = !isRandomOrder;
        String newMode = isRandomOrder ? "random" : "sequential";
        
        player.displayClientMessage(Component.translatable("autoskullswitcher.message.order_changed", newMode), false);
        AutoSkullSwitcher.LOGGER.info("Switched to {} order mode", newMode);
        
        // 如果当前正在运行，重新开始以应用新的排序
        if (isActive) {
            stopSkullSwitching(player);
            startSkullSwitching(player);
        }
    }
    
    /**
     * 扫描玩家背包中的头颅物品
     */
    private static void scanForSkulls(Player player) {
        availableSkullSlots.clear();
        Inventory inventory = player.getInventory();

        // 遍历背包中的所有物品
        for (int i = 0; i < inventory.getContainerSize(); i++) {
            ItemStack stack = inventory.getItem(i);

            if (isSkullItem(stack)) {
                // 存储槽位索引而不是物品副本
                availableSkullSlots.add(i);
                AutoSkullSwitcher.LOGGER.debug("Found skull item: {} in slot {}", stack.getDisplayName().getString(), i);
            }
        }

        AutoSkullSwitcher.LOGGER.info("Found {} skull items in inventory", availableSkullSlots.size());
    }
    
    /**
     * 检查物品是否为头颅类型
     */
    private static boolean isSkullItem(ItemStack stack) {
        if (stack.isEmpty()) {
            return false;
        }
        
        // 检查常见的头颅物品类型
        return stack.getItem() == Items.PLAYER_HEAD ||
               stack.getItem() == Items.ZOMBIE_HEAD ||
               stack.getItem() == Items.SKELETON_SKULL ||
               stack.getItem() == Items.WITHER_SKELETON_SKULL ||
               stack.getItem() == Items.CREEPER_HEAD ||
               stack.getItem() == Items.DRAGON_HEAD ||
               stack.getItem() instanceof PlayerHeadItem;
    }
    
    /**
     * 装备下一个头颅 - 使用真正的装备更换方式
     */
    private static void equipNextSkull(Player player) {
        // 重新扫描以确保头颅还在背包中
        scanForSkulls(player);

        if (availableSkullSlots.isEmpty()) {
            stopSkullSwitching(player);
            player.displayClientMessage(Component.translatable("autoskullswitcher.message.no_skulls_available"), false);
            return;
        }

        // 确保索引有效
        if (currentSkullIndex >= availableSkullSlots.size()) {
            currentSkullIndex = 0;
        }

        int skullSlot = availableSkullSlots.get(currentSkullIndex);
        Inventory inventory = player.getInventory();
        ItemStack skullToEquip = inventory.getItem(skullSlot);

        // 检查头颅是否还在该槽位
        if (skullToEquip.isEmpty() || !isSkullItem(skullToEquip)) {
            // 头颅已被移动或消失，重新扫描
            scanForSkulls(player);
            if (availableSkullSlots.isEmpty()) {
                stopSkullSwitching(player);
                player.displayClientMessage(Component.translatable("autoskullswitcher.message.no_skulls_available"), false);
                return;
            }
            currentSkullIndex = 0;
            skullSlot = availableSkullSlots.get(currentSkullIndex);
            skullToEquip = inventory.getItem(skullSlot);
        }

        // 执行真正的装备更换
        performRealEquipSwap(player, skullSlot, skullToEquip);

        AutoSkullSwitcher.LOGGER.debug("Equipped skull: {} from slot {}", skullToEquip.getDisplayName().getString(), skullSlot);

        // 更新索引
        currentSkullIndex = (currentSkullIndex + 1) % availableSkullSlots.size();

        // 如果是随机模式且已经循环完一轮，重新打乱
        if (isRandomOrder && currentSkullIndex == 0 && availableSkullSlots.size() > 1) {
            Collections.shuffle(availableSkullSlots);
        }
    }

    /**
     * 执行真正的装备更换 - 模拟玩家手动装备的过程
     */
    private static void performRealEquipSwap(Player player, int skullSlot, ItemStack skullToEquip) {
        Inventory inventory = player.getInventory();
        ItemStack currentHelmet = player.getItemBySlot(EquipmentSlot.HEAD);

        // 从背包中取出头颅
        ItemStack skullStack = inventory.removeItem(skullSlot, skullToEquip.getCount());

        // 如果当前有头盔，放回背包
        if (!currentHelmet.isEmpty()) {
            // 尝试放回原来的槽位
            if (inventory.getItem(skullSlot).isEmpty()) {
                inventory.setItem(skullSlot, currentHelmet);
            } else {
                // 原槽位被占用，尝试添加到其他位置
                if (!inventory.add(currentHelmet)) {
                    // 背包满了，丢弃当前头盔
                    player.drop(currentHelmet, false);
                }
            }
        }

        // 装备新头颅
        player.setItemSlot(EquipmentSlot.HEAD, skullStack);

        // 标记背包已更改
        inventory.setChanged();
    }
    
    /**
     * 启动定时切换任务
     */
    private static void startScheduledSwitching(Player player) {
        if (switchingTask != null && !switchingTask.isCancelled()) {
            switchingTask.cancel(false);
        }
        
        switchingTask = scheduler.scheduleWithFixedDelay(
            () -> {
                // 确保在主线程中执行
                Minecraft.getInstance().execute(() -> {
                    if (isActive && player != null && !player.isRemoved()) {
                        equipNextSkull(player);
                    }
                });
            },
            ModConfig.getSwitchingInterval(),
            ModConfig.getSwitchingInterval(),
            TimeUnit.SECONDS
        );
    }
    
    /**
     * 获取当前状态信息
     */
    public static boolean isActive() {
        return isActive;
    }
    
    public static boolean isRandomOrder() {
        return isRandomOrder;
    }
    
    public static int getAvailableSkullsCount() {
        return availableSkullSlots.size();
    }
    
    /**
     * 清理资源
     */
    public static void cleanup() {
        if (switchingTask != null && !switchingTask.isCancelled()) {
            switchingTask.cancel(true);
        }
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}