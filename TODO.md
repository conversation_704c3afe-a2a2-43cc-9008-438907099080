用户背景：
了解编程知识；
没有minecrage neoforged 插件开发经验



服务器地图背景：服务器会有很多自定义头颅送给玩家。

问题：我想开发一个插件，能够世界用户背包里的所有头颅。然后按照顺序，或者可以随机顺序（你弄一个按钮），自动的装备到用户的头上（就是头盔）. 这样实现一个自动换头颅的MOD。 替换过程是后台的，用户可以做其他事情。

具体游戏内操作：
用户按L之后，根据默认设置的头颅更换顺序，进行自动更换头颅操作，每个间隔1秒（可自定义）


游戏版本：1.21.4
官方文档，这里有你需要引用的正确的包：https://docs.neoforged.net/docs/1.21.4/gettingstarted/modfiles

---
当前项目：
当前项目是一个金块寻找MOD，已经可以正常打包，正常绑定快捷键，功能也正常。
所以你需要删除之前的金块插件逻辑代码，复用按键绑定代码等这些基础步骤。
然后开始开发上面我要求的 自动更换头颅插件。
语言支持中文/英文/德语

